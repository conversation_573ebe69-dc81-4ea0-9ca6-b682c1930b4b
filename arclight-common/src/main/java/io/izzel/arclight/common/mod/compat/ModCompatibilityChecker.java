package io.izzel.arclight.common.mod.compat;

import io.izzel.arclight.common.mod.ArclightMod;
import io.izzel.arclight.common.mod.util.log.ArclightI18nLogger;
import org.apache.logging.log4j.Logger;

/**
 * Mod compatibility checker for Luminara
 * Critical mod conflict detection that stops server on incompatibility
 */
public class ModCompatibilityChecker {

    private static final Logger LOGGER = ArclightI18nLogger.getLogger("ModCompatibilityChecker");
    private static ModCompatibilityChecker instance;

    private ModCompatibilityChecker() {}

    public static ModCompatibilityChecker getInstance() {
        if (instance == null) {
            instance = new ModCompatibilityChecker();
        }
        return instance;
    }

    public void initialize() {
        LOGGER.debug("Checking critical mod compatibility...");
        checkCriticalConflicts();
    }

    private void checkCriticalConflicts() {
        // JEI and NEI conflict - these mods provide similar functionality and will cause crashes
        checkFatalConflict("jei", "nei", "J<PERSON>", "NEI");
    }

    private void checkFatalConflict(String mod1Id, String mod2Id, String mod1Name, String mod2Name) {
        if (isModLoaded(mod1Id) && isModLoaded(mod2Id)) {
            LOGGER.error("mod.conflict-detected", mod1Name, mod2Name);
            LOGGER.error("mod.conflict-fatal");

            // Stop server immediately
            System.exit(1);
        }
    }

    private boolean isModLoaded(String modId) {
        return ArclightMod.isModLoaded(modId);
    }
}
